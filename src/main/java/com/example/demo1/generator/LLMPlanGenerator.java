package com.example.demo1.generator;

import com.example.demo1.plan.ExecutionPlan;
import com.example.demo1.plan.impl.PortableExecutionPlan;
import com.example.demo1.step.ExecutionStep;
import com.example.demo1.step.impl.VectorSearchStep;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonParser;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

public class LLMPlanGenerator {
//    private final OpenAIClient llmClient;
//    private final EmbeddingService embeddingService;
//
//    public ExecutionPlan generatePlan(String question) {
//        // 1. 调用LLM分解问题
//        List<String> subQuestions = decomposeQuestion(question);
//
//        // 2. 构建执行步骤
//        List<ExecutionStep> steps = new ArrayList<>();
//        for (String subQ : subQuestions) {
//            steps.add(new VectorSearchStep(
//                    "default_kb", // 实际应通过路由获取
//                    subQ,
//                    embeddingService.embed(subQ),
//                    3
//            ));
//        }
//
//        return new PortableExecutionPlan(
//                UUID.randomUUID().toString(),
//                question,
//                steps,
//                new Date()
//        );
//    }

    private List<String> decomposeQuestion(String question) {
        // 模拟LLM调用，实际替换为真实API
        String mockResponse = "{\"sub_questions\":[\"电池容量是多少？\",\"充电速度如何？\"]}";
        JsonArray arr = new JsonParser()
                .parse(mockResponse)
                .getAsJsonObject()
                .getAsJsonArray("sub_questions");

        List<String> results = new ArrayList<>();
        for (JsonElement elem : arr) {
            results.add(elem.getAsString());
        }
        return results;
    }
}