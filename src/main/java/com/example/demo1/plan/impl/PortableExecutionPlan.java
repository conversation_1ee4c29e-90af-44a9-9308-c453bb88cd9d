package com.example.demo1.plan.impl;


import com.example.demo1.plan.ExecutionPlan;
import com.example.demo1.step.ExecutionStep;
import com.google.gson.*;

import java.util.*;

// 基于Gson的实现
public class PortableExecutionPlan implements ExecutionPlan {
    private final String planId;
    private final String originalQuestion;
    private final List<ExecutionStep> steps;
    private final Date createdAt;

    public PortableExecutionPlan(String planId, String originalQuestion,
                                 List<ExecutionStep> steps, Date createdAt) {
        this.planId = Objects.requireNonNull(planId);
        this.originalQuestion = Objects.requireNonNull(originalQuestion);
        this.steps = Collections.unmodifiableList(new ArrayList<>(steps));
        this.createdAt = new Date(createdAt.getTime()); // 防御性拷贝
    }

    @Override
    public String getPlanId() {
        return "";
    }

    @Override
    public String getOriginalQuestion() {
        return "";
    }

    @Override
    public List<ExecutionStep> getSteps() {
        return null;
    }

    @Override
    public Date getCreatedAt() {
        return null;
    }

    @Override
    public String serialize() {
        JsonObject json = new JsonObject();
        json.addProperty("planId", planId);
        json.addProperty("question", originalQuestion);

        JsonArray stepsArray = new JsonArray();
        for (ExecutionStep step : steps) {
            stepsArray.add(step.toJson());
        }
        json.add("steps", stepsArray);

        json.addProperty("createdAt", createdAt.getTime());
        return new Gson().toJson(json);
    }

    public static PortableExecutionPlan deserialize(String jsonStr) {
        JsonObject json = new JsonParser().parse(jsonStr).getAsJsonObject();

        List<ExecutionStep> steps = new ArrayList<>();
        for (JsonElement elem : json.getAsJsonArray("steps")) {
            steps.add(ExecutionStep.fromJson(elem.getAsJsonObject()));
        }

        return new PortableExecutionPlan(
                json.get("planId").getAsString(),
                json.get("question").getAsString(),
                steps,
                new Date(json.get("createdAt").getAsLong())
        );
    }
}