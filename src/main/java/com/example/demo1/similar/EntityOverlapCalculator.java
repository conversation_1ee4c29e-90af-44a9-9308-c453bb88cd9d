package com.example.demo1.similar;

import java.util.HashSet;
import java.util.Set;

public class EntityOverlapCalculator {

    // Jaccard相似度
    public static double calculateJaccardSimilarity(Set<String> set1, Set<String> set2) {
        Set<String> intersection = new HashSet<>(set1);
        intersection.retainAll(set2);

        Set<String> union = new HashSet<>(set1);
        union.addAll(set2);

        return union.isEmpty() ? 0.0 : (double) intersection.size() / union.size();
    }

    // 重叠系数
    public static double calculateOverlapCoefficient(Set<String> set1, Set<String> set2) {
        Set<String> intersection = new HashSet<>(set1);
        intersection.retainAll(set2);

        int minSize = Math.min(set1.size(), set2.size());
        return minSize == 0 ? 0.0 : (double) intersection.size() / minSize;
    }

    // Dice系数
    public static double calculateDiceCoefficient(Set<String> set1, Set<String> set2) {
        Set<String> intersection = new HashSet<>(set1);
        intersection.retainAll(set2);

        int totalSize = set1.size() + set2.size();
        return totalSize == 0 ? 0.0 : 2.0 * intersection.size() / totalSize;
    }
}