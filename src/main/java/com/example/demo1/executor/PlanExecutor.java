package com.example.demo1.executor;

import com.example.demo1.enetiy.Chunk;
import com.example.demo1.plan.ExecutionPlan;
import com.example.demo1.step.ExecutionStep;
import com.example.demo1.step.impl.VectorSearchStep;

import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

public class PlanExecutor {
    private final ExecutorService executor;
    private final Map<String, StepExecutor> stepExecutors;

    public PlanExecutor(int threadPoolSize) {
        this.executor = Executors.newFixedThreadPool(threadPoolSize);
        this.stepExecutors = new HashMap<>();
        stepExecutors.put("VECTOR_SEARCH", new VectorSearchExecutor());
    }

    public List<Chunk> execute(ExecutionPlan plan) {
        List<Future<List<Chunk>>> futures = new ArrayList<>();
        for (ExecutionStep step : plan.getSteps()) {
            futures.add(executor.submit(() ->
                    stepExecutors.get(step.getType()).execute(step)
            ));
        }

        List<Chunk> results = new ArrayList<>();
        for (Future<List<Chunk>> future : futures) {
            try {
                results.addAll(future.get());
            } catch (InterruptedException | ExecutionException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("Step execution failed", e);
            }
        }
        return results;
    }

    public interface StepExecutor {
        List<Chunk> execute(ExecutionStep step);
    }

    private static class VectorSearchExecutor implements StepExecutor {
        @Override
        public List<Chunk> execute(ExecutionStep step) {
            VectorSearchStep searchStep = (VectorSearchStep) step;
            // 实际向量库查询逻辑
            return Collections.singletonList(
                    new Chunk("mock_id", "mock_content",
                            searchStep.getEmbedding(), Collections.emptySet())
            );
        }
    }
}