package com.example.demo1.config;

import com.example.demo1.executor.PlanExecutor;
import com.example.demo1.generator.LLMPlanGenerator;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class AppConfig {

//    @Bean
//    public OpenAIClient openAIClient(
//            @Value("${openai.api.key}") String apiKey,
//            @Value("${openai.timeout:5000}") int timeoutMs) {
//        return new OpenAIClient(apiKey, timeoutMs);
//    }
//
//    @Bean
//    public LLMPlanGenerator planGenerator(OpenAIClient client) {
//        return new LLMPlanGenerator(client, new EmbeddingService());
//    }
//
//    @Bean(destroyMethod = "shutdown")
//    public PlanExecutor planExecutor(@Value("${thread.pool.size:8}") int poolSize) {
//        return new PlanExecutor(poolSize);
//    }

    @Bean
    public Gson gson() {
        return new GsonBuilder()
                .setDateFormat("yyyy-MM-dd HH:mm:ss")
                .create();
    }
}