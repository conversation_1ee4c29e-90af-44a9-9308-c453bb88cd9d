package com.example.demo1.enetiy;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.Set;

// 知识库切片
public class Chunk {
    private final String id;
    private final String content;
    private final float[] embedding;
    private final Set<String> tags;

    // 全参数构造器
    public Chunk(String id, String content, float[] embedding, Set<String> tags) {
        this.id = id;
        this.content = content;
        this.embedding = Arrays.copyOf(embedding, embedding.length); // 防御性拷贝
        this.tags = Collections.unmodifiableSet(new HashSet<>(tags));
    }

    // getters...
    public String getId() { return id; }
    public float[] getEmbedding() { return Arrays.copyOf(embedding, embedding.length); }
    // 其他getter...
}