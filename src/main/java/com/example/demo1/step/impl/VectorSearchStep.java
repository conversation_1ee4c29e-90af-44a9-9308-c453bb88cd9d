package com.example.demo1.step.impl;

import com.example.demo1.step.ExecutionStep;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;

import java.util.Arrays;
import java.util.Objects;

// 向量搜索步骤
public class VectorSearchStep implements ExecutionStep {
    private final String kbName;
    private final String subQuestion;
    private final float[] embedding;
    private final int topK;

    public VectorSearchStep(String kbName, String subQuestion, float[] embedding, int topK) {
        this.kbName = Objects.requireNonNull(kbName);
        this.subQuestion = Objects.requireNonNull(subQuestion);
        this.embedding = Arrays.copyOf(embedding, embedding.length);
        this.topK = topK;
        if (topK <= 0) throw new IllegalArgumentException("topK must be positive");
    }

    @Override
    public String getType() { return "VECTOR_SEARCH"; }

    @Override
    public JsonObject toJson() {
        JsonObject json = new JsonObject();
        json.addProperty("type", getType());

        JsonObject params = new JsonObject();
        params.addProperty("kbName", kbName);
        params.addProperty("subQuestion", subQuestion);

        JsonArray embeddingArr = new JsonArray();
        for (float f : embedding) {
            embeddingArr.add(f);
        }
        params.add("embedding", embeddingArr);

        params.addProperty("topK", topK);
        json.add("params", params);
        return json;
    }

    public static VectorSearchStep fromJson(JsonObject params) {
        JsonArray embeddingArr = params.getAsJsonArray("embedding");
        float[] embedding = new float[embeddingArr.size()];
        for (int i = 0; i < embedding.length; i++) {
            embedding[i] = embeddingArr.get(i).getAsFloat();
        }

        return new VectorSearchStep(
                params.get("kbName").getAsString(),
                params.get("subQuestion").getAsString(),
                embedding,
                params.get("topK").getAsInt()
        );
    }

    // getters...
    public String getKbName() { return kbName; }
    public float[] getEmbedding() { return Arrays.copyOf(embedding, embedding.length); }
}