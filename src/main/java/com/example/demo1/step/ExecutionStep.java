package com.example.demo1.step;

import com.example.demo1.step.impl.VectorSearchStep;
import com.google.gson.JsonObject;

public interface ExecutionStep {
    String getType();
    JsonObject toJson();

    // 工厂方法
    static ExecutionStep fromJson(JsonObject json) {
        String type = json.get("type").getAsString();
        JsonObject params = json.get("params").getAsJsonObject();

        switch (type) {
            case "VECTOR_SEARCH":
                return VectorSearchStep.fromJson(params);
            // 其他步骤类型...
            default:
                throw new IllegalArgumentException("Unknown step type: " + type);
        }
    }
}