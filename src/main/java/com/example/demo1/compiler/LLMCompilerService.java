package com.example.demo1.compiler;

import java.util.concurrent.CompletableFuture;

public class LLMCompilerService {
    private final PlanGenerator planGenerator;
    private final PlanExecutor planExecutor;
    
    public LLMCompilerService(LLMService llmService, KnowledgeBaseManager kbManager, 
                             ChunkRetriever chunkRetriever) {
        this.planGenerator = new PlanGenerator(llmService, kbManager);
        this.planExecutor = new PlanExecutor(chunkRetriever, llmService, kbManager);
    }
    
    public CompletableFuture<ExecutionResult> search(String query) {
        return planGenerator.generatePlan(query)
            .thenCompose(plan -> planExecutor.execute(plan));
    }
    
    public void shutdown() {
        planExecutor.shutdown();
    }
}