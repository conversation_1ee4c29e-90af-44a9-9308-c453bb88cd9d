package com.example.demo1.compiler;

import java.util.List;
import java.util.Map;

public class ExecutionPlan {
    private String planId;
    private String originalQuery;
    private List<SubQuery> subQueries;
    private Map<String, Object> context;
    private long timestamp;
    
    public ExecutionPlan(String planId, String originalQuery) {
        this.planId = planId;
        this.originalQuery = originalQuery;
        this.timestamp = System.currentTimeMillis();
    }
    
    // getters and setters
    public String getPlanId() { return planId; }
    public String getOriginalQuery() { return originalQuery; }
    public List<SubQuery> getSubQueries() { return subQueries; }
    public void setSubQueries(List<SubQuery> subQueries) { this.subQueries = subQueries; }
    public Map<String, Object> getContext() { return context; }
    public void setContext(Map<String, Object> context) { this.context = context; }
    public long getTimestamp() { return timestamp; }
}