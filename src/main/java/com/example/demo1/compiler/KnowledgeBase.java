package com.example.demo1.compiler;

import java.util.List;
import java.util.Set;

public class KnowledgeBase {
    private String id;
    private String name;
    private Set<String> tags;
    private List<Chunk> chunks;
    
    public KnowledgeBase(String id, String name, Set<String> tags) {
        this.id = id;
        this.name = name;
        this.tags = tags;
    }
    
    // getters and setters
    public String getId() { return id; }
    public String getName() { return name; }
    public Set<String> getTags() { return tags; }
    public List<Chunk> getChunks() { return chunks; }
    public void setChunks(List<Chunk> chunks) { this.chunks = chunks; }
}