package com.example.demo1.compiler;

import java.util.List;

public class SubQueryResult {
    private String subQueryId;
    private List<Chunk> retrievedChunks;
    private List<String> matchedKnowledgeBases; // 新增：匹配的知识库
    private String answer;
    private String error;
    private long executionTime;
    
    public SubQueryResult(String subQueryId) {
        this.subQueryId = subQueryId;
    }
    
    // getters and setters
    public String getSubQueryId() { return subQueryId; }
    public List<Chunk> getRetrievedChunks() { return retrievedChunks; }
    public void setRetrievedChunks(List<Chunk> retrievedChunks) { this.retrievedChunks = retrievedChunks; }
    public List<String> getMatchedKnowledgeBases() { return matchedKnowledgeBases; }
    public void setMatchedKnowledgeBases(List<String> matchedKnowledgeBases) { this.matchedKnowledgeBases = matchedKnowledgeBases; }
    public String getAnswer() { return answer; }
    public void setAnswer(String answer) { this.answer = answer; }
    public String getError() { return error; }
    public void setError(String error) { this.error = error; }
    public long getExecutionTime() { return executionTime; }
    public void setExecutionTime(long executionTime) { this.executionTime = executionTime; }
}