package com.example.demo1.compiler;

import java.util.*;
import java.util.concurrent.*;

public class PlanExecutor {
    private final ExecutorService executorService;
    private final ChunkRetriever chunkRetriever;
    private final LLMService llmService;
    private final KnowledgeBaseManager kbManager;
    
    public PlanExecutor(ChunkRetriever chunkRetriever, LLMService llmService, KnowledgeBaseManager kbManager) {
        this.executorService = Executors.newFixedThreadPool(10);
        this.chunkRetriever = chunkRetriever;
        this.llmService = llmService;
        this.kbManager = kbManager;
    }
    
    public CompletableFuture<ExecutionResult> execute(ExecutionPlan plan) {
        return CompletableFuture.supplyAsync(() -> {
            ExecutionResult result = new ExecutionResult(plan.getPlanId());
            Map<String, CompletableFuture<SubQueryResult>> futures = new HashMap<>();
            Map<String, SubQueryResult> completedResults = new ConcurrentHashMap<>();
            
            // 按优先级和依赖关系执行子查询
            List<SubQuery> sortedQueries = new ArrayList<>(plan.getSubQueries());
            sortedQueries.sort(Comparator.comparingInt(SubQuery::getPriority));
            
            for (SubQuery subQuery : sortedQueries) {
                CompletableFuture<SubQueryResult> future = executeSubQueryWithDependencies(
                    subQuery, completedResults);
                futures.put(subQuery.getId(), future);
            }
            
            // 等待所有子查询完成
            List<SubQueryResult> subResults = new ArrayList<>();
            for (Map.Entry<String, CompletableFuture<SubQueryResult>> entry : futures.entrySet()) {
                try {
                    SubQueryResult subResult = entry.getValue().get(30, TimeUnit.SECONDS);
                    subResults.add(subResult);
                    completedResults.put(entry.getKey(), subResult);
                } catch (Exception e) {
                    SubQueryResult errorResult = new SubQueryResult(entry.getKey());
                    errorResult.setError("执行失败: " + e.getMessage());
                    subResults.add(errorResult);
                }
            }
            
            result.setSubResults(subResults);
            
            // 合成最终答案
            String finalAnswer = synthesizeAnswer(plan.getOriginalQuery(), subResults);
            result.setFinalAnswer(finalAnswer);
            
            return result;
        }, executorService);
    }
    
    private CompletableFuture<SubQueryResult> executeSubQueryWithDependencies(
            SubQuery subQuery, Map<String, SubQueryResult> completedResults) {
        
        return CompletableFuture.supplyAsync(() -> {
            // 等待依赖完成
            if (subQuery.getDependencies() != null) {
                for (String depId : subQuery.getDependencies()) {
                    while (!completedResults.containsKey(depId)) {
                        try {
                            Thread.sleep(100); // 简单的轮询等待
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                            break;
                        }
                    }
                }
            }
            
            // 根据类型执行不同的操作
            switch (subQuery.getType()) {
                case KNOWLEDGE_BASE_ROUTING:
                    return routeKnowledgeBases(subQuery);
                case CHUNK_RETRIEVAL:
                    return retrieveChunks(subQuery, completedResults);
                case ANSWER_GENERATION:
                    return generateAnswer(subQuery, completedResults);
                default:
                    SubQueryResult errorResult = new SubQueryResult(subQuery.getId());
                    errorResult.setError("未知的子查询类型");
                    return errorResult;
            }
        }, executorService);
    }
    
    /**
     * 根据子问题路由到合适的知识库
     */
    private SubQueryResult routeKnowledgeBases(SubQuery subQuery) {
        SubQueryResult result = new SubQueryResult(subQuery.getId());
        
        try {
            List<String> matchedKBs = matchKnowledgeBasesByQuery(subQuery.getQuery());
            result.setMatchedKnowledgeBases(matchedKBs);
            result.setAnswer("路由到知识库: " + String.join(", ", matchedKBs));
        } catch (Exception e) {
            result.setError("知识库路由失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 根据知识库列表和子问题检索chunks
     */
    private SubQueryResult retrieveChunks(SubQuery subQuery, Map<String, SubQueryResult> completedResults) {
        SubQueryResult result = new SubQueryResult(subQuery.getId());
        
        try {
            // 获取路由阶段的结果
            String routingId = subQuery.getDependencies().get(0);
            SubQueryResult routingResult = completedResults.get(routingId);
            
            if (routingResult == null || routingResult.getMatchedKnowledgeBases() == null) {
                result.setError("无法获取知识库路由结果");
                return result;
            }
            
            List<Chunk> relevantChunks = retrieveChunksByKnowledgeBases(
                routingResult.getMatchedKnowledgeBases(), subQuery.getQuery());
            
            result.setRetrievedChunks(relevantChunks);
            result.setAnswer("检索到 " + relevantChunks.size() + " 个相关chunks");
            
        } catch (Exception e) {
            result.setError("chunk检索失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 生成答案
     */
    private SubQueryResult generateAnswer(SubQuery subQuery, Map<String, SubQueryResult> completedResults) {
        SubQueryResult result = new SubQueryResult(subQuery.getId());
        
        try {
            // 获取检索阶段的结果
            String retrievalId = subQuery.getDependencies().get(0);
            SubQueryResult retrievalResult = completedResults.get(retrievalId);
            
            if (retrievalResult == null || retrievalResult.getRetrievedChunks() == null) {
                result.setError("无法获取chunk检索结果");
                return result;
            }
            
            String answer = generateAnswerFromChunks(subQuery.getQuery(), retrievalResult.getRetrievedChunks());
            result.setAnswer(answer);
            
        } catch (Exception e) {
            result.setError("答案生成失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 根据子问题匹配知识库
     */
    private List<String> matchKnowledgeBasesByQuery(String query) {
        List<String> matchedKBs = new ArrayList<>();
        List<KnowledgeBase> allKBs = kbManager.getAllKnowledgeBases();
        
        for (KnowledgeBase kb : allKBs) {
            double similarity = calculateSimilarity(query, kb);
            if (similarity > 0.7) { // 阈值可配置
                matchedKBs.add(kb.getId());
            }
        }
        
        return matchedKBs;
    }
    
    /**
     * 根据知识库列表和子问题检索chunks
     */
    private List<Chunk> retrieveChunksByKnowledgeBases(List<String> knowledgeBaseIds, String query) {
        List<Chunk> relevantChunks = new ArrayList<>();
        
        for (String kbId : knowledgeBaseIds) {
            List<Chunk> chunks = chunkRetriever.retrieve(kbId, query, 5);
            relevantChunks.addAll(chunks);
        }
        
        return relevantChunks;
    }
    
    private String generateAnswerFromChunks(String query, List<Chunk> chunks) {
        StringBuilder context = new StringBuilder();
        for (Chunk chunk : chunks) {
            context.append(chunk.getContent()).append("\n\n");
        }
        
        String prompt = String.format(
            "基于以下上下文信息回答问题：\n上下文：%s\n问题：%s\n请提供准确、简洁的答案。",
            context.toString(), query);
        
        return llmService.chat(prompt);
    }
    
    private double calculateSimilarity(String query, KnowledgeBase kb) {
        // 实现相似度计算逻辑
        return 0.8; // 占位符
    }
    
    private String synthesizeAnswer(String originalQuery, List<SubQueryResult> subResults) {
        StringBuilder allAnswers = new StringBuilder();
        for (SubQueryResult subResult : subResults) {
            if (subResult.getAnswer() != null && 
                subResult.getSubQueryId().startsWith("answer_")) { // 只取答案生成阶段的结果
                allAnswers.append(subResult.getAnswer()).append("\n\n");
            }
        }
        
        String prompt = String.format(
            "基于以下子问题的答案，综合回答原始问题：\n原始问题：%s\n子答案：%s\n请提供完整、连贯的最终答案。",
            originalQuery, allAnswers.toString());
        
        return llmService.chat(prompt);
    }
    
    public void shutdown() {
        executorService.shutdown();
    }
}