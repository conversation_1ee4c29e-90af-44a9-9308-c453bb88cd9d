package com.example.demo1.compiler;

import java.util.List;

public class ExecutionResult {
    private String planId;
    private List<SubQueryResult> subResults;
    private String finalAnswer;
    private long executionTime;
    private boolean success;
    
    public ExecutionResult(String planId) {
        this.planId = planId;
        this.success = true;
    }
    
    // getters and setters
    public String getPlanId() { return planId; }
    public List<SubQueryResult> getSubResults() { return subResults; }
    public void setSubResults(List<SubQueryResult> subResults) { this.subResults = subResults; }
    public String getFinalAnswer() { return finalAnswer; }
    public void setFinalAnswer(String finalAnswer) { this.finalAnswer = finalAnswer; }
    public long getExecutionTime() { return executionTime; }
    public void setExecutionTime(long executionTime) { this.executionTime = executionTime; }
    public boolean isSuccess() { return success; }
    public void setSuccess(boolean success) { this.success = success; }
}