package com.example.demo1.compiler;

import java.util.Map;

public class Chunk {
    private String id;
    private String content;
    private String sourceFile;
    private Map<String, Object> metadata;
    private double[] embedding;
    
    public Chunk(String id, String content, String sourceFile) {
        this.id = id;
        this.content = content;
        this.sourceFile = sourceFile;
    }
    
    // getters and setters
    public String getId() { return id; }
    public String getContent() { return content; }
    public String getSourceFile() { return sourceFile; }
    public Map<String, Object> getMetadata() { return metadata; }
    public void setMetadata(Map<String, Object> metadata) { this.metadata = metadata; }
    public double[] getEmbedding() { return embedding; }
    public void setEmbedding(double[] embedding) { this.embedding = embedding; }
}