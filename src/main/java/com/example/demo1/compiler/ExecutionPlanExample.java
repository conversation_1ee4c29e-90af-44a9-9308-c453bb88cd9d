package com.example.demo1.compiler;

import java.util.*;

public class ExecutionPlanExample {
    
    public static void main(String[] args) {
        // 模拟用户问题
        String userQuery = "如何提高供应链效率并降低成本？";
        
        // 生成执行计划示例
        ExecutionPlan plan = generateSamplePlan(userQuery);
        
        // 打印执行计划
        printExecutionPlan(plan);
    }
    
    public static ExecutionPlan generateSamplePlan(String userQuery) {
        String planId = "plan_12345";
        ExecutionPlan plan = new ExecutionPlan(planId, userQuery);
        
        // 模拟LLM分解后的子问题
        List<String> subQuestions = Arrays.asList(
            "供应链效率的关键指标有哪些？",
            "如何优化供应链流程？", 
            "降低供应链成本的方法有哪些？"
        );
        
        List<SubQuery> subQueries = new ArrayList<>();
        int priority = 0;
        
        for (int i = 0; i < subQuestions.size(); i++) {
            String subQuestion = subQuestions.get(i);
            
            // 1. 知识库路由子查询
            SubQuery routingQuery = new SubQuery("routing_" + i, subQuestion, 
                SubQuery.SubQueryType.KNOWLEDGE_BASE_ROUTING);
            routingQuery.setPriority(priority++);
            subQueries.add(routingQuery);
            
            // 2. chunk检索子查询（依赖路由结果）
            SubQuery retrievalQuery = new SubQuery("retrieval_" + i, subQuestion, 
                SubQuery.SubQueryType.CHUNK_RETRIEVAL);
            retrievalQuery.setPriority(priority++);
            retrievalQuery.setDependencies(Arrays.asList(routingQuery.getId()));
            subQueries.add(retrievalQuery);
            
            // 3. 答案生成子查询（依赖检索结果）
            SubQuery answerQuery = new SubQuery("answer_" + i, subQuestion, 
                SubQuery.SubQueryType.ANSWER_GENERATION);
            answerQuery.setPriority(priority++);
            answerQuery.setDependencies(Arrays.asList(retrievalQuery.getId()));
            subQueries.add(answerQuery);
        }
        
        plan.setSubQueries(subQueries);
        
        // 设置上下文信息
        Map<String, Object> context = new HashMap<>();
        context.put("domain", "供应链管理");
        context.put("language", "中文");
        context.put("maxChunksPerKB", 5);
        plan.setContext(context);
        
        return plan;
    }
    
    public static void printExecutionPlan(ExecutionPlan plan) {
        System.out.println("=== 执行计划 ===");
        System.out.println("计划ID: " + plan.getPlanId());
        System.out.println("原始问题: " + plan.getOriginalQuery());
        System.out.println("创建时间: " + new Date(plan.getTimestamp()));
        System.out.println("上下文: " + plan.getContext());
        System.out.println();
        
        System.out.println("=== 子查询执行步骤 ===");
        List<SubQuery> sortedQueries = new ArrayList<>(plan.getSubQueries());
        sortedQueries.sort(Comparator.comparingInt(SubQuery::getPriority));
        
        for (SubQuery subQuery : sortedQueries) {
            System.out.println("步骤 " + (subQuery.getPriority() + 1) + ":");
            System.out.println("  ID: " + subQuery.getId());
            System.out.println("  类型: " + getTypeDescription(subQuery.getType()));
            System.out.println("  问题: " + subQuery.getQuery());
            
            if (subQuery.getDependencies() != null && !subQuery.getDependencies().isEmpty()) {
                System.out.println("  依赖: " + subQuery.getDependencies());
            }
            
            System.out.println("  预期输出: " + getExpectedOutput(subQuery.getType()));
            System.out.println();
        }
        
        System.out.println("=== 执行流程图 ===");
        printExecutionFlow(plan);
    }
    
    private static String getTypeDescription(SubQuery.SubQueryType type) {
        switch (type) {
            case KNOWLEDGE_BASE_ROUTING:
                return "知识库路由";
            case CHUNK_RETRIEVAL:
                return "文档片段检索";
            case ANSWER_GENERATION:
                return "答案生成";
            default:
                return "未知类型";
        }
    }
    
    private static String getExpectedOutput(SubQuery.SubQueryType type) {
        switch (type) {
            case KNOWLEDGE_BASE_ROUTING:
                return "匹配的知识库ID列表";
            case CHUNK_RETRIEVAL:
                return "相关文档片段列表";
            case ANSWER_GENERATION:
                return "子问题的答案文本";
            default:
                return "未知输出";
        }
    }
    
    private static void printExecutionFlow(ExecutionPlan plan) {
        System.out.println("原始问题: " + plan.getOriginalQuery());
        System.out.println("    ↓");
        System.out.println("LLM分解为子问题");
        System.out.println("    ↓");
        
        List<SubQuery> subQueries = plan.getSubQueries();
        Map<String, List<SubQuery>> groupedQueries = new HashMap<>();
        
        // 按子问题分组
        for (SubQuery sq : subQueries) {
            String baseId = sq.getId().replaceAll("_(routing|retrieval|answer)_\\d+", "");
            String group = sq.getId().contains("routing") ? "routing" : 
                          sq.getId().contains("retrieval") ? "retrieval" : "answer";
            
            groupedQueries.computeIfAbsent(group, k -> new ArrayList<>()).add(sq);
        }
        
        // 打印每个阶段
        String[] phases = {"routing", "retrieval", "answer"};
        String[] phaseNames = {"知识库路由阶段", "文档检索阶段", "答案生成阶段"};
        
        for (int i = 0; i < phases.length; i++) {
            System.out.println(phaseNames[i] + ":");
            List<SubQuery> phaseQueries = groupedQueries.get(phases[i]);
            if (phaseQueries != null) {
                for (SubQuery sq : phaseQueries) {
                    System.out.println("  - " + sq.getId() + ": " + sq.getQuery());
                }
            }
            if (i < phases.length - 1) {
                System.out.println("    ↓");
            }
        }
        
        System.out.println("    ↓");
        System.out.println("合成最终答案");
    }
}