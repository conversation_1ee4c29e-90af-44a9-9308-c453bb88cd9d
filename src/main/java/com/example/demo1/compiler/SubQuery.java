package com.example.demo1.compiler;

import java.util.List;

public class SubQuery {
    private String id;
    private String query;
    private List<String> targetKnowledgeBases;
    private int priority;
    private List<String> dependencies;
    private SubQueryType type; // 新增：子查询类型
    
    public SubQuery(String id, String query) {
        this.id = id;
        this.query = query;
    }
    
    public SubQuery(String id, String query, SubQueryType type) {
        this.id = id;
        this.query = query;
        this.type = type;
    }
    
    // getters and setters
    public String getId() { return id; }
    public String getQuery() { return query; }
    public List<String> getTargetKnowledgeBases() { return targetKnowledgeBases; }
    public void setTargetKnowledgeBases(List<String> targetKnowledgeBases) { 
        this.targetKnowledgeBases = targetKnowledgeBases; 
    }
    public int getPriority() { return priority; }
    public void setPriority(int priority) { this.priority = priority; }
    public List<String> getDependencies() { return dependencies; }
    public void setDependencies(List<String> dependencies) { this.dependencies = dependencies; }
    public SubQueryType getType() { return type; }
    public void setType(SubQueryType type) { this.type = type; }
    
    public enum SubQueryType {
        KNOWLEDGE_BASE_ROUTING,  // 知识库路由
        CHUNK_RETRIEVAL,         // chunk检索
        ANSWER_GENERATION        // 答案生成
    }
}