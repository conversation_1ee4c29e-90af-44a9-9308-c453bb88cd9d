package com.example.demo1;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.connection.RedisConnectionFactory;

@SpringBootTest
public class RedisConnectionTest {

    @Autowired
    private RedisConnectionFactory redisConnectionFactory;

    @Test
    public void testConnection() {
        try (RedisConnection connection = redisConnectionFactory.getConnection()) {
            // 执行 PING 命令
            String response = connection.ping();
            if ("PONG".equals(response)) {
                System.out.println("Redis 连接成功！");
            } else {
                System.out.println("Redis 返回异常响应：" + response);
            }
        } catch (Exception e) {
            System.err.println("Redis 连接失败：" + e.getMessage());
        }
    }
}